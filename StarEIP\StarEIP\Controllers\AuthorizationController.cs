﻿using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using StarEIP.Models;
using System.Linq.Expressions;
using System.Security.Claims;
using StarEIP.Models.Auth;
using StarEIP.Services.Auth;
using StarEIP.Models.App;

namespace StarEIP.Controllers
{
    [Route("api/authorizations")]
    [ApiController]
    [Authorize]
    public class AuthorizationController : ControllerBase
    {
        private readonly StarEipDbContext starEipDbContext;
        private readonly ILogger<AuthorizationController> logger;

        public AuthorizationController(StarEipDbContext starEipDbContext, ILogger<AuthorizationController> logger)
        {
            this.starEipDbContext = starEipDbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int? childId = null)
        {
            try
            {
                IQueryable<AuthorizationDto> query = GetAuthorizationQuery(childId);

                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);
                starEipDbContext.ChangeTracker.LazyLoadingEnabled = true;
                starEipDbContext.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;

                loadOptions.PrimaryKey = [nameof(AuthorizationDto.Id)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(query, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get authorizations");
                throw;
            }
        }

        [HttpGet("child/{childId}")]
        public async Task<IActionResult> GetByChildId(int childId)
        {
            var query = GetAuthorizationQuery(childId);
            return Ok(await query.ToListAsync());
        }

        private IQueryable<AuthorizationDto> GetAuthorizationQuery(int? childId = null)
        {
            var currentUserId = User.GetUserId();
            var canViewAllAuth = User.HasClaim(UserPermission.PermissionClaimName, UserPermission.ViewAllAuth);

            var query = starEipDbContext.Authorizations
                .Where(a => a.DeletedOn == null) // Exclude soft-deleted records
                .Include(a => a.User)
                .Include(a => a.Child)
                .ThenInclude(c => c.ReferringPhysician)
                .Include(a => a.ScStatusType)
                .Select(a => new AuthorizationDto
                {
                    //auth properties
                    Id = a.Id,
                    ChildName = a.Child.FirstName + " " + a.Child.LastName,
                    AuthType = a.AuthType,
                    AuthNumber = a.AuthNumber,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    Units = a.Units,
                    ChildId = a.ChildId,
                    StatusId = a.StatusId,
                    UserId = a.UserId,
                    UserName = a.User.UserName,
                    UserFullName = a.User.FirstName + " " + a.User.LastName,

                    // SC-specific properties
                    ScStatus = a.ScStatus,
                    ScStatusName = a.ScStatusType != null ? a.ScStatusType.Name : null,
                    StatusLastUpdated = a.StatusLastUpdated,
                    FollowUpDate = a.FollowUpDate,
                    ScStatusLastUpdated = a.ScStatusLastUpdated
                });

            if (childId.HasValue)
            {
                query = query.Where(a => a.ChildId == childId.Value);
            }

            if (!canViewAllAuth)
            {
                query = query.Where(a => a.UserId == currentUserId);
            }

            return query;
        }

        [HttpGet("Status")]
        public async Task<IActionResult> GetStatus()
        {
            var status = await starEipDbContext.ChildStatuses.ToListAsync();
            return Ok(status);
        }

        [HttpGet("statuses")]
        public async Task<IActionResult> GetAuthorizationStatuses()
        {
            var statuses = await starEipDbContext.AuthorizationStatuses.ToListAsync();
            return Ok(statuses);
        }

        [HttpGet("sc-status-types")]
        public async Task<IActionResult> GetScStatusTypes()
        {
            try
            {
                var scStatusTypes = await starEipDbContext.ScStatusTypes
                    .OrderBy(s => s.SortOrder)
                    .ToListAsync();

                // If no data exists, seed some default values
                if (!scStatusTypes.Any())
                {
                    var defaultStatuses = new[]
                    {
                        new ScStatusType { Name = "Pending", SortOrder = 1 },
                        new ScStatusType { Name = "In Progress", SortOrder = 2 },
                        new ScStatusType { Name = "Completed", SortOrder = 3 },
                        new ScStatusType { Name = "Needs Follow-up", SortOrder = 4 },
                        new ScStatusType { Name = "On Hold", SortOrder = 5 }
                    };

                    starEipDbContext.ScStatusTypes.AddRange(defaultStatuses);
                    await starEipDbContext.SaveChangesAsync();

                    scStatusTypes = await starEipDbContext.ScStatusTypes
                        .OrderBy(s => s.SortOrder)
                        .ToListAsync();
                }

                return Ok(scStatusTypes);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error fetching SC status types");
                return StatusCode(500, new { Message = "An error occurred while fetching SC status types", Error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                var authorization = await GetAuthorizationQuery(null).SingleOrDefaultAsync(a => a.Id == id);

                if (authorization == null)
                    return NotFound();

                return Ok(authorization);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error in get authorization by id");
                throw;
            }
        }

        [HttpPut("Update")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> UpdateAuthorization([FromForm] int key, [FromForm] string values)
        {
            var authorization = await starEipDbContext.Authorizations.SingleAsync(r => r.Id == key);
            JsonConvert.PopulateObject(values, authorization);
            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }

        [HttpPost("create")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> Create([FromBody] Authorization authorization)
        {
            // Ensure that Child and User are not being set directly
            authorization.Status = null;
            authorization.Child = null;
            authorization.User = null;

            starEipDbContext.Authorizations.Add(authorization);
            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }

        [HttpPost("update/{id}")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> Update(int id, [FromBody] Authorization updatedAuthorization)
        {
            var authorization = await starEipDbContext.Authorizations.FindAsync(id);
            if (authorization == null)
            {
                return NotFound();
            }

            // Update properties
            authorization.ChildId = updatedAuthorization.ChildId;
            authorization.StatusId = updatedAuthorization.StatusId;
            authorization.AuthType = updatedAuthorization.AuthType;
            authorization.UserId = updatedAuthorization.UserId;
            authorization.AuthNumber = updatedAuthorization.AuthNumber;
            authorization.StartDate = updatedAuthorization.StartDate;
            authorization.EndDate = updatedAuthorization.EndDate;
            authorization.Units = updatedAuthorization.Units;

            await starEipDbContext.SaveChangesAsync();
            return Ok(authorization);
        }



        public record AuthorizationStatusUpdateRequest(int AuthorizationId, string StatusId);

        [HttpPost("{id}/status")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> UpdateStatus(int id, [FromBody] AuthorizationStatusUpdateRequest request)
        {
            var authorization = await starEipDbContext.Authorizations.FindAsync(id);
            if (authorization == null)
            {
                return NotFound();
            }

            authorization.StatusId = request.StatusId;
            await starEipDbContext.SaveChangesAsync();

            var updatedAuthorization = await GetAuthorizationQuery(null).FirstOrDefaultAsync(a => a.Id == id);
            return Ok(updatedAuthorization);
        }

        [HttpDelete("Delete")]
        [Authorize(Policy = nameof(UserPermission.AllowManageAuthorization))]
        public async Task<IActionResult> Delete([FromForm] int key)
        {
            logger.LogInformation("Entering Delete Authorization");
            try
            {
                var authorization = await starEipDbContext.Authorizations.SingleAsync(r => r.Id == key);
                authorization.DeletedOn = DateTime.Now;
                authorization.DeletedBy = User.GetUserId();
                await starEipDbContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in delete authorization");
                return BadRequest(ex.Message);
            }
        }

        public record ScStatusUpdateRequest(int ScStatus, DateTime? FollowUpDate);

        [HttpPost("{id}/sc-status")]
        public async Task<IActionResult> UpdateScStatus(int id, [FromBody] ScStatusUpdateRequest request)
        {
            try
            {
                var authorization = await starEipDbContext.Authorizations.FindAsync(id);
                if (authorization == null)
                {
                    return NotFound();
                }

                // Verify that the ScStatus exists in ScStatusType table
                var scStatusExists = await starEipDbContext.ScStatusTypes
                    .AnyAsync(s => s.Id == request.ScStatus);

                if (!scStatusExists)
                {
                    return BadRequest($"ScStatus with ID {request.ScStatus} does not exist");
                }

                authorization.ScStatus = request.ScStatus;
                authorization.FollowUpDate = request.FollowUpDate;
                authorization.ScStatusLastUpdated = DateTime.Now;
                await starEipDbContext.SaveChangesAsync();

                var updatedAuthorization = await GetAuthorizationQuery().FirstOrDefaultAsync(a => a.Id == id);
                return Ok(updatedAuthorization);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating SC status for authorization {AuthorizationId}", id);
                return StatusCode(500, new { Message = "An error occurred while updating SC status", Error = ex.Message });
            }
        }

        [HttpPost("Reconcile")]
        [AllowAnonymous]
        public async Task<IActionResult> ReconcileAuthorizations([FromQuery] string? therapistNpi = null)
        {
            try
            {
                var query = from importAuth in starEipDbContext.ImportChildInfoAuthorizations
                            join child in starEipDbContext.Children on importAuth.EiChildId equals child.ProgramId.ToString()
                            join auth in starEipDbContext.Authorizations on new { ChildId = child.Id, AuthNumber = importAuth.AuthorizationNumber } equals new { auth.ChildId, auth.AuthNumber } into authJoin
                            from auth in authJoin.DefaultIfEmpty()
                            join user in starEipDbContext.Users on importAuth.TherapistNpi equals user.Npi
                            where importAuth.ServiceCoordinatorCompany == "Star EIP LLC" &&
                                  importAuth.Program.StartsWith("Service Coordination -") &&
                                  (auth == null || auth.StatusId == "Active")
                            select new
                            {
                                ImportAuth = importAuth,
                                Authorization = auth,
                                User = user,
                                Child = child
                            };

                var existingMatches = await query.ToListAsync();

                // Update existing authorizations
                foreach (var record in existingMatches.Where(a => a.Authorization != null))
                {
                    record.Authorization.AuthNumber = record.ImportAuth.AuthorizationNumber;
                    record.Authorization.StartDate = record.ImportAuth.StartDate;
                    record.Authorization.EndDate = record.ImportAuth.EndDate;
                    record.Authorization.Units = record.ImportAuth.TotalSessionsAuthorized;
                    record.Authorization.UserId = record.User.Id;
                    record.Authorization.StatusId = "Active";
                }

                // Create new authorizations
                foreach (var record in existingMatches.Where(a => a.Authorization == null))
                {
                    var existingAuth = await starEipDbContext
                        .Authorizations
                        .FirstOrDefaultAsync(a => a.ChildId == record.Child.Id && string.IsNullOrWhiteSpace(a.AuthNumber) && a.StatusId == "New");
                    if (existingAuth == null)
                    {
                        existingAuth = new Authorization { ChildId = record.Child.Id };
                        starEipDbContext.Authorizations.Add(existingAuth);
                    }

                    string authType = record.ImportAuth.Program.Contains("Initial") ? "ISC" : "OSC";

                    existingAuth.AuthNumber = record.ImportAuth.AuthorizationNumber;
                    existingAuth.StartDate = record.ImportAuth.StartDate;
                    existingAuth.EndDate = record.ImportAuth.EndDate;
                    existingAuth.Units = record.ImportAuth.TotalSessionsAuthorized;
                    existingAuth.UserId = record.User.Id;
                    existingAuth.StatusId = "Active";
                    existingAuth.AuthType = authType;
                }

                await starEipDbContext.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during authorization reconciliation");
                return StatusCode(500, new { Message = "An error occurred during reconciliation", Error = ex.Message });
            }
        }
    }
}
