"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Group,
  Text,
  ActionIcon,
  Tabs,
  Divider,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import PatientTable from "@/app/(admin)/admin/patients/PatientTable";
import PatientForm from "@/app/(admin)/admin/patients/PatientForm";
import axios from "axios";
import { showNotification } from "@/app/Utils/notificationUtils";
import urlHelpers from "@/app/urlHelpers";
import { ChildDetailsDtoSchema } from "@/api/types";

interface ChildMappingDrawerProps {
  eiNumber: string;
  onMappingSuccess: () => void;
  renderDetails?: () => React.ReactNode;
  buttonText?: React.ReactNode;
  buttonSize?: "xs" | "sm" | "md" | "lg" | "xl";
  childData?: {
    childFirstName?: string;
    childLastName?: string;
    dob?: string | Date | null;
    address?: string;
    city?: string;
    state?: string;
    zip?: string;
    gender?: string;
    primaryLanguage?: string;
    referralReason?: string;
    referralMethod?: string;
    guardianFirstName?: string;
    guardianLastName?: string;
    guardianPhone?: string;
  };
}

const ChildMappingDrawer: React.FC<ChildMappingDrawerProps> = ({
  eiNumber,
  onMappingSuccess,
  buttonText = "Map to Child",
  buttonSize = "xs",
  childData,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [activeTab, setActiveTab] = useState<string | null>("search");
  const [physicians, setPhysicians] = useState<any[]>([]);

  useEffect(() => {
    // Fetch physicians when the drawer is opened
    if (opened) {
      const fetchPhysicians = async () => {
        try {
          const response = await axios.get(
            urlHelpers.getAbsoluteURL("api/physicians"),
          );
          setPhysicians(response.data.data);
        } catch (error) {
          console.error("Error fetching physicians:", error);
        }
      };
      fetchPhysicians();
    }
  }, [opened]);

  const handleLinkChild = async (child: ChildDetailsDtoSchema | null) => {
    if (!child) return;

    // Validate inputs before making the API call
    if (!eiNumber || eiNumber.trim() === "") {
      showNotification("error", "EI Number cannot be empty");
      return;
    }

    if (!child.id || child.id <= 0) {
      showNotification("error", "Invalid child selected");
      return;
    }

    try {
      console.log("Mapping EI Number:", eiNumber, "to Child ID:", child.id);
      const response = await axios.post(
        urlHelpers.getAbsoluteURL("api/children/map-ei-number"),
        {
          eiNumber: eiNumber.trim(),
          childId: child.id,
        },
      );

      showNotification(
        "success",
        response.data.message || "Successfully mapped EI Number to child",
      );

      onMappingSuccess();
      close();
    } catch (error: any) {
      console.error("Error mapping child:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.title ||
        (typeof error.response?.data === "string"
          ? error.response.data
          : null) ||
        error.message ||
        "Failed to map child";
      showNotification("error", errorMessage);
    }
  };

  const handleCreateChild = async (child: ChildDetailsDtoSchema) => {
    try {
      // First create the child
      const createUrl = urlHelpers.getAbsoluteURL("api/children/create");
      const createResponse = await axios.post(createUrl, child);

      if (createResponse.status === 200) {
        // Then map the EI number to the newly created child
        const newChildId = createResponse.data.id;
        await axios.post(
          urlHelpers.getAbsoluteURL("api/children/map-ei-number"),
          {
            eiNumber: eiNumber,
            childId: newChildId,
          },
        );

        showNotification(
          "success",
          "Successfully created new child and mapped EI Number",
        );

        onMappingSuccess();
        close();
      }
    } catch (error: any) {
      console.error("Error creating child:", error);
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.title ||
        (typeof error.response?.data === "string"
          ? error.response.data
          : null) ||
        error.message ||
        "Failed to create child";
      showNotification("error", errorMessage);
    }
  };

  let mappedGender: string | undefined = undefined;
  if (childData?.gender) {
    const genderLower = childData.gender.toLowerCase();
    if (genderLower === "m" || genderLower === "male") {
      mappedGender = "M";
    } else if (genderLower === "f" || genderLower === "female") {
      mappedGender = "F";
    } else {
      mappedGender = childData.gender;
    }
  }

  // Prepare initial data for the new child form
  const initialChildData: Partial<ChildDetailsDtoSchema> = {
    id: 0,
    status: "New",
    firstName: childData?.childFirstName,
    lastName: childData?.childLastName,
    dateOfBirth: childData?.dob
      ? new Date(childData.dob).toISOString()
      : undefined,
    gender: mappedGender, // Use the mapped gender value
    primaryLanguage: childData?.primaryLanguage, // Include the primary language
    fullAddress: childData?.address
      ? `${childData.address}${childData.city ? ", " + childData.city : ""}${childData.state ? ", " + childData.state : ""}${childData.zip ? " " + childData.zip : ""}`
      : undefined,
    reasonForReferral: childData?.referralReason, // Include the referral reason
    referralMethod: childData?.referralMethod || "HUB", // Use provided referral method or default to HUB
    parentName:
      childData?.guardianFirstName && childData?.guardianLastName
        ? `${childData.guardianFirstName} ${childData.guardianLastName}`
        : childData?.guardianFirstName || childData?.guardianLastName,
    parentPhoneNumber: childData?.guardianPhone, // Include the guardian phone number
    programId: eiNumber ? parseInt(eiNumber, 10) || undefined : undefined, // Map EI child ID to program ID if it's a valid number
  };

  // Check if buttonText is a React element (icon)
  const isIcon = React.isValidElement(buttonText);

  return (
    <>
      {isIcon ? (
        <ActionIcon
          onClick={open}
          size={buttonSize}
          color="gray"
          variant="subtle"
          radius="xl"
        >
          {buttonText}
        </ActionIcon>
      ) : (
        <Button onClick={open} size={buttonSize}>
          {buttonText}
        </Button>
      )}

      <Drawer
        opened={opened}
        onClose={close}
        position="right"
        size="50%"
        padding="xs"
        styles={{
          content: { display: "flex", flexDirection: "column" },
          body: {
            display: "flex",
            flexDirection: "column",
            flex: 1,
            overflow: "auto",
          },
        }}
        overlayProps={{ backgroundOpacity: 0.1, blur: 0 }}
        closeOnClickOutside
        title={`Map EI Number: ${eiNumber}`}
      >
        <Tabs
          value={activeTab}
          onChange={setActiveTab}
          styles={{
            root: {
              display: "flex",
              flexDirection: "column",
              flex: 1,
              overflow: "auto",
            },
          }}
        >
          <Tabs.List>
            <Tabs.Tab value="search">Search Existing Patient</Tabs.Tab>
            <Tabs.Tab value="create">Add New Patient</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel
            value="search"
            pt="xs"
            style={{
              height: "100%",
              display: "flex",
              flex: 1,
              flexGrow: 1,
              flexDirection: "column",
              width: "100%",
              overflow: "auto",
            }}
          >
            <PatientTable
              showToolbar
              item={null}
              onFocusedChildChanging={handleLinkChild}
              options={{
                showDateOfBirth: true,
                showHeaderFilter: true,
                defaultFilter: (() => {
                  if (!childData?.dob) return undefined;

                  const date = new Date(childData.dob);
                  // Check if the date is valid
                  if (isNaN(date.getTime())) return undefined;

                  return ["dateOfBirth", "=", date];
                })(),
              }}
            />
          </Tabs.Panel>

          <Tabs.Panel
            value="create"
            pt="xs"
            style={{
              height: "100%",
              display: "flex",
              flex: 1,
              flexGrow: 1,
              flexDirection: "column",
              width: "100%",
            }}
          >
            <PatientForm
              onSubmit={handleCreateChild}
              onCancel={() => setActiveTab("search")}
              isEditMode={false}
              initialData={initialChildData as ChildDetailsDtoSchema}
              physicians={physicians}
            />
          </Tabs.Panel>
        </Tabs>
      </Drawer>
    </>
  );
};

export default ChildMappingDrawer;
