"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { createStore } from "devextreme-aspnet-data-nojquery";
import DataGrid, {
  Column,
  Pager,
  Paging,
  SearchPanel,
  RemoteOperations,
  HeaderFilter,
  ColumnChooser,
  Sorting,
  ColumnFixing,
  FilterPanel,
  FilterBuilderPopup,
  Export,
  Toolbar,
  Item,
  Button as DgButton,
  DataGridRef,
} from "devextreme-react/data-grid";
import Button from "devextreme-react/button";
import urlHelpers from "../../../urlHelpers";
import dataGridExport from "../../../Utils/dataGridExport";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Authorization } from "../../../../../types/Authorization";
import { Popup } from "devextreme-react/popup";
import AuthorizationDetailsPopup from "./AuthorizationDetailsPopup";
import { useHasPermission } from "@/app/store/AuthStore";
import { useAuthorizationStore } from "@/app/(admin)/admin/authorizations/AuthorizationStore";

const serviceUrl = urlHelpers.getAbsoluteURL("api/authorizations");

interface AuthorizationsTableProps {
  childId?: number;
  onSelectAuthorizationChange?: (authorization: Authorization | null) => void;
  onChildNameClick?: (childId: number) => void;
}

const AuthorizationsTable = ({
  childId,
  onSelectAuthorizationChange,
  onChildNameClick,
}: AuthorizationsTableProps) => {
  const { setDataGridRef, setRemoteDataSource, remoteDataSource } =
    useAuthorizationStore();
  const allowManageAuthorization = useHasPermission("AllowManageAuthorization");
  const router = useRouter();
  const dataGridRef = useRef<DataGridRef>(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingAuthorization, setEditingAuthorization] =
    useState<Authorization | null>(null);
  const [selectedAuthorization, setSelectedAuthorization] =
    useState<Authorization | null>(null);

  const loadAuthorizationDataSource = useCallback(async () => {
    setRemoteDataSource(
      createStore({
        key: "id",
        loadUrl: childId ? `${serviceUrl}?childId=${childId}` : serviceUrl,
        deleteUrl: `${serviceUrl}/Delete`,
        onBeforeSend: (_, s) => {
          s.headers = {
            Authorization: `Bearer ${localStorage.getItem("jwtToken")}`,
          };
        },
        errorHandler(e) {
          if (e.message === "Unauthorized") {
            router.push("/login");
          }
        },
      }),
    );
  }, [childId, router]);

  useEffect(() => {
    setDataGridRef(dataGridRef); // Store the ref in Zustand on mount
  }, [setDataGridRef]);

  useEffect(() => {
    loadAuthorizationDataSource();
  }, [loadAuthorizationDataSource]);

  const handleFocusedRowChanging = (e: any) => {
    setSelectedAuthorization(e.row && e.row.data);
    onSelectAuthorizationChange?.(e.row?.data ?? null);
  };

  const handleRowUpdated = (e: any) => {
    console.log("Row updated", e);
  };


  const handleEditClick = useCallback(() => {
    setEditingAuthorization(selectedAuthorization);
    setIsFormVisible(true);
  }, [selectedAuthorization]);

  const handleFormClose = useCallback(() => {
    setIsFormVisible(false);
    setEditingAuthorization(null);
  }, []);

  const handleAuthorizationSubmitted = useCallback(() => {
    loadAuthorizationDataSource();
    setIsFormVisible(false);
    setEditingAuthorization(null);
  }, [loadAuthorizationDataSource]);

  const renderIdLink = useCallback((e: any) => {
    return <Link href={`/admin/authorizations/${e.data.id}`}>{e.data.id}</Link>;
  }, []);

  const renderChildNameLink = useCallback((e: any) => {
    if (!onChildNameClick || !e.data.childId) {
      return e.data.childName;
    }

    return (
      <span
        style={{
          color: "#1976d2",
          cursor: "pointer",
          textDecoration: "underline"
        }}
        onClick={() => onChildNameClick(e.data.childId)}
      >
        {e.data.childName}
      </span>
    );
  }, [onChildNameClick]);

  return (
    <>
      <DataGrid
        remoteOperations
        ref={dataGridRef}
        dataSource={remoteDataSource}
        onExporting={dataGridExport}
        height="100%"
        width="100%"
        allowColumnReordering={true}
        allowColumnResizing={true}
        columnAutoWidth={true}
        showBorders={true}
        columnResizingMode={"widget"}
        twoWayBindingEnabled
        rowAlternationEnabled
        focusedRowEnabled
        autoNavigateToFocusedRow
        onFocusedRowChanged={handleFocusedRowChanging}
        onRowUpdated={handleRowUpdated}
      >
        <Toolbar>
          <Item location="before">
            <Button icon="refresh" onClick={loadAuthorizationDataSource} />
          </Item>
          <Item name="searchPanel" locateInMenu="auto" location="before" />
          <Item name="groupPanel" locateInMenu="auto" location="before" />
          <Item name="exportButton" locateInMenu="auto" location="after" />
          <Item name="applyFilterButton" locateInMenu="auto" location="after" />
          <Item name="revertButton" locateInMenu="auto" location="after" />
          <Item name="saveButton" locateInMenu="auto" location="after" />
          <Item
            name="columnChooserButton"
            locateInMenu="auto"
            location="after"
          />
          <Item location="before">
            <Button
              icon="edit"
              visible={allowManageAuthorization}
              disabled={!selectedAuthorization}
              onClick={handleEditClick}
            />
          </Item>
        </Toolbar>
        <RemoteOperations groupPaging={true} />
        <ColumnFixing enabled={true} />
        <SearchPanel visible width={250} />
        <HeaderFilter visible />
        <ColumnChooser enabled />
        <Sorting mode="multiple" />
        <ColumnFixing />
        <Paging defaultPageSize={40} />
        <Pager showPageSizeSelector />
        <FilterPanel visible />
        <FilterBuilderPopup />
        <Export enabled={true} />

        <Column
          dataField="id"
          caption="ID"
          allowEditing={false}
          cellRender={renderIdLink}
          visible={allowManageAuthorization}
        />

        {!childId && (
          <Column
            dataField="childName"
            caption="Child Name"
            dataType="string"
            cellRender={renderChildNameLink}
          />
        )}

        <Column
          dataField="childId"
          caption="Child ID"
          dataType="number"
          visible={false}
        />
        <Column dataField="statusId" caption="Stage" dataType="string" />
        <Column dataField="scStatusName" caption="Status" dataType="string" />
        <Column dataField="authType" caption="Type" dataType="string" />
        <Column dataField="programId" caption="Program ID" dataType="number" />
        <Column
          dataField="userId"
          caption="SC"
          dataType="number"
          visible={false}
        />
        <Column dataField="userName" caption="SC" dataType="string" visible={allowManageAuthorization}/>
        <Column dataField="authNumber" caption="Auth #" dataType="string" />
        <Column dataField="startDate" caption="Start Date" dataType="date" />
        <Column dataField="endDate" caption="End Date" dataType="date" />
        <Column dataField="units" caption="Units" dataType="number" />
        <Column dataField="scStatusLastUpdated" caption="Status Updated" dataType="datetime" format="shortDateShortTime"/>
        <Column type="buttons" cssClass={"text-align-right"}>
          <DgButton name="edit" />
          <DgButton name="delete" />
        </Column>


      </DataGrid>

      {isFormVisible && (
        <Popup
          visible={isFormVisible}
          onHiding={handleFormClose}
          title={
            editingAuthorization
              ? "Edit Authorization"
              : "Add New Authorization"
          }
          showCloseButton={true}
          width={800}
          height={"auto"}
        >
          <AuthorizationDetailsPopup
            closeDetails={handleAuthorizationSubmitted}
            initialData={editingAuthorization || undefined}
            isEditMode={!!editingAuthorization}
            childId={childId} // Pass the childId here
          />
        </Popup>
      )}
    </>
  );
};

export default AuthorizationsTable;
