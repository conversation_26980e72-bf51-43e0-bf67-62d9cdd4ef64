import { create } from "zustand";
import { ChildDetailsDtoSchema } from "@/api/types";

export interface ChildrenStore {
  dataGridRef: any;
  setDataGridRef: (ref: any) => void;
  selectedChild: ChildDetailsDtoSchema | null;
  setSelectedChild: (child: ChildDetailsDtoSchema) => void;
}

export const usePatientStore = create<ChildrenStore>((set, get) => {
  const setDataGridRef = (ref: any) => {
    set({ dataGridRef: ref });
  };

  const selectChild = (child: ChildDetailsDtoSchema) => {
    set({ selectedChild: child });
  };

  return {
    dataGridRef: null,
    setDataGridRef,
    selectedChild: null,
    setSelectedChild: selectChild,
  };
});
